[gd_scene load_steps=13 format=3 uid="uid://oys430ji88lr"]

[ext_resource type="Script" uid="uid://bbtuesaulb563" path="res://src/Script/Player/PlayerController.cs" id="1_vhadf"]
[ext_resource type="Shader" uid="uid://bpnlyq75a2iih" path="res://src/Shaders/VoidBackground/void_background.gdshader" id="2_k2co4"]
[ext_resource type="Script" uid="uid://ciax0omtarbew" path="res://src/Script/VoidBackground/VoidBackgroundController.cs" id="3_sm553"]
[ext_resource type="Script" uid="uid://bb8m7gsqit418" path="res://src/Script/Ui/MainScenePopupController.gd" id="4_lq0qa"]
[ext_resource type="Texture2D" uid="uid://balle06os3rqy" path="res://Assets/image/player_image/Soldier with shadows/Soldier.png" id="5_vhadf"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_3f673"]
shader = ExtResource("2_k2co4")
shader_parameter/noise_scale = 2.5
shader_parameter/fog_density = 1.4
shader_parameter/fog_color = Color(0.719042, 0.39165, 1, 0)
shader_parameter/offset = Vector2(0, 0)

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_k2co4"]

[sub_resource type="Animation" id="Animation_sm553"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PlayerSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="Animation" id="Animation_3f673"]
resource_name = "attack"
length = 0.3
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PlayerSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.0333333, 0.1, 0.133333, 0.2, 0.233333, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [18, 19, 20, 21, 22, 23, 18]
}

[sub_resource type="Animation" id="Animation_lq0qa"]
resource_name = "idle"
length = 0.8
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PlayerSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(-0.0666667, 0.1, 0.266667, 0.433333, 0.6, 0.766667),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3, 4, 5]
}

[sub_resource type="Animation" id="Animation_wquk2"]
resource_name = "walk"
length = 0.8
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PlayerSprite:frame")
tracks/0/interp = 0
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(-0.0333333, 0.0666667, 0.2, 0.3, 0.4, 0.5, 0.566667, 0.666667, 0.766667),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [16, 9, 10, 11, 12, 13, 14, 15, 16]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_cqylb"]
_data = {
&"RESET": SubResource("Animation_sm553"),
&"attack": SubResource("Animation_3f673"),
&"idle": SubResource("Animation_lq0qa"),
&"walk": SubResource("Animation_wquk2")
}

[node name="PlayerBehavior" type="Node2D"]

[node name="Player" type="CharacterBody2D" parent="."]
position = Vector2(414, 408)
collision_mask = 14
script = ExtResource("1_vhadf")

[node name="PlayerCamera2D" type="Camera2D" parent="Player"]
position = Vector2(0, -24)
zoom = Vector2(0.5, 0.5)

[node name="voidBackground_root" type="CanvasLayer" parent="Player/PlayerCamera2D"]
layer = -10
visible = false

[node name="voidBackground" type="ColorRect" parent="Player/PlayerCamera2D/voidBackground_root"]
z_index = 2
material = SubResource("ShaderMaterial_3f673")
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
script = ExtResource("3_sm553")
fog_density = 1.4

[node name="Control" type="Control" parent="Player/PlayerCamera2D"]
visible = false
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -1152.0
offset_top = -648.0
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(2, 2)
script = ExtResource("4_lq0qa")

[node name="TriggerButtons" type="HBoxContainer" parent="Player/PlayerCamera2D/Control"]
z_index = 100
layout_mode = 0
offset_left = 12.0
offset_top = 14.0
offset_right = 353.0
offset_bottom = 105.0
theme_override_constants/separation = 15

[node name="SettingsButton" type="Button" parent="Player/PlayerCamera2D/Control/TriggerButtons"]
custom_minimum_size = Vector2(80, 50)
layout_mode = 2
text = "设置"

[node name="InventoryButton" type="Button" parent="Player/PlayerCamera2D/Control/TriggerButtons"]
custom_minimum_size = Vector2(80, 50)
layout_mode = 2
text = "背包"

[node name="DialogButton" type="Button" parent="Player/PlayerCamera2D/Control/TriggerButtons"]
custom_minimum_size = Vector2(80, 50)
layout_mode = 2
text = "对话"

[node name="PopupOverlay" type="Control" parent="Player/PlayerCamera2D/Control"]
z_index = 100
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="DialogPopup" type="Panel" parent="Player/PlayerCamera2D/Control/PopupOverlay"]
visible = false
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -344.0
offset_top = -200.0
offset_right = 344.0
offset_bottom = 200.0
grow_horizontal = 2
grow_vertical = 2

[node name="Dialogue" type="Label" parent="Player/PlayerCamera2D/Control/PopupOverlay/DialogPopup"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -170.5
offset_right = 170.5
offset_bottom = 35.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 25
text = "对话"
horizontal_alignment = 1

[node name="DialogueButton" type="VBoxContainer" parent="Player/PlayerCamera2D/Control/PopupOverlay/DialogPopup"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -152.0
offset_top = 76.0
offset_bottom = 204.0
grow_horizontal = 0
grow_vertical = 2
theme_override_constants/separation = 15

[node name="Send" type="Button" parent="Player/PlayerCamera2D/Control/PopupOverlay/DialogPopup/DialogueButton"]
custom_minimum_size = Vector2(80, 50)
layout_mode = 2
text = "发送"

[node name="Return" type="Button" parent="Player/PlayerCamera2D/Control/PopupOverlay/DialogPopup/DialogueButton"]
custom_minimum_size = Vector2(80, 50)
layout_mode = 2
text = "返回"

[node name="​​SettingsPop-up" type="Panel" parent="Player/PlayerCamera2D/Control/PopupOverlay"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -344.0
offset_top = -200.0
offset_right = 344.0
offset_bottom = 200.0
grow_horizontal = 2
grow_vertical = 2

[node name="GameSettings" type="Label" parent="Player/PlayerCamera2D/Control/PopupOverlay/​​SettingsPop-up"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -170.5
offset_right = 170.5
offset_bottom = 35.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 25
text = "游戏设置"
horizontal_alignment = 1

[node name="SetButton" type="VBoxContainer" parent="Player/PlayerCamera2D/Control/PopupOverlay/​​SettingsPop-up"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.5
anchor_top = 0.49
anchor_right = 0.5
anchor_bottom = 0.49
offset_left = -120.0
offset_top = -104.0
offset_right = 120.0
offset_bottom = 141.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 15

[node name="SaveGame" type="Button" parent="Player/PlayerCamera2D/Control/PopupOverlay/​​SettingsPop-up/SetButton"]
custom_minimum_size = Vector2(80, 50)
layout_mode = 2
text = "保存游戏"

[node name="GameSettings" type="Button" parent="Player/PlayerCamera2D/Control/PopupOverlay/​​SettingsPop-up/SetButton"]
custom_minimum_size = Vector2(80, 50)
layout_mode = 2
text = "设置"

[node name="ExitGame" type="Button" parent="Player/PlayerCamera2D/Control/PopupOverlay/​​SettingsPop-up/SetButton"]
custom_minimum_size = Vector2(80, 50)
layout_mode = 2
text = "退出游戏"

[node name="ReturnToStart​" type="Button" parent="Player/PlayerCamera2D/Control/PopupOverlay/​​SettingsPop-up/SetButton"]
custom_minimum_size = Vector2(80, 50)
layout_mode = 2
text = "返回到开始页面"

[node name="Return" type="Button" parent="Player/PlayerCamera2D/Control/PopupOverlay/​​SettingsPop-up/SetButton"]
custom_minimum_size = Vector2(80, 50)
layout_mode = 2
text = "返回"

[node name="Backpack" type="Panel" parent="Player/PlayerCamera2D/Control/PopupOverlay"]
visible = false
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -344.0
offset_top = -200.0
offset_right = 344.0
offset_bottom = 200.0
grow_horizontal = 2
grow_vertical = 2

[node name="Label" type="Label" parent="Player/PlayerCamera2D/Control/PopupOverlay/Backpack"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -49.0
offset_right = 49.0
offset_bottom = 35.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 25
text = "背包"
horizontal_alignment = 1

[node name="SettingsButton" type="Button" parent="Player/PlayerCamera2D/Control/PopupOverlay/Backpack"]
custom_minimum_size = Vector2(80, 50)
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -80.0
offset_top = -50.0
grow_horizontal = 0
grow_vertical = 0
text = "返回"

[node name="PlayerSprite" type="Sprite2D" parent="Player"]
texture_filter = 1
scale = Vector2(5, 5)
texture = ExtResource("5_vhadf")
hframes = 9
vframes = 7

[node name="PlayerCollisionShap2D" type="CollisionShape2D" parent="Player"]
z_index = 2
position = Vector2(2, 29)
rotation = 1.5708
shape = SubResource("CapsuleShape2D_k2co4")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Player"]
libraries = {
&"": SubResource("AnimationLibrary_cqylb")
}
