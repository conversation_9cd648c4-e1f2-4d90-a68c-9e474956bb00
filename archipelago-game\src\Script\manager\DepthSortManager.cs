/*
 * 深度排序管理器 - 2D等距视角深度渲染解决方案
 * 负责管理基于Y坐标的深度排序，确保角色和建筑物正确的前后关系
 */

using Godot;

namespace ArchipelagoGame.Manager
{
    /// <summary>
    /// 深度排序管理器 - 处理2D等距视角的深度渲染
    /// 使用Godot 4.x的Y-Sort功能实现基于Y坐标的自动深度排序
    /// </summary>
    public partial class DepthSortManager : Node
    {
        #region 单例模式

        /// <summary>单例实例</summary>
        public static DepthSortManager Instance { get; private set; }

        #endregion

        #region 导出属性

        /// <summary>是否启用调试信息输出</summary>
        [Export]
        public bool EnableDebugOutput { get; set; } = false;

        /// <summary>深度排序的基础偏移值</summary>
        [Export]
        public int BaseDepthOffset { get; set; } = 1000;

        #endregion

        #region 私有字段

        /// <summary>游戏世界根节点引用</summary>
        private Node2D _gameWorldRoot;

        /// <summary>玩家节点引用</summary>
        private CharacterBody2D _player;

        #endregion

        #region Godot生命周期

        /// <summary>
        /// 节点准备就绪时初始化
        /// </summary>
        public override void _Ready()
        {
            // 设置单例
            if (Instance == null)
            {
                Instance = this;
            }
            else
            {
                QueueFree();
                return;
            }

            // 延迟初始化，确保场景完全加载
            CallDeferred(MethodName.InitializeDepthSorting);
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 初始化深度排序系统
        /// </summary>
        public void InitializeDepthSorting()
        {
            GD.Print("=== 深度排序管理器初始化开始 ===");

            // 尝试查找节点，如果失败则延迟重试
            if (!TryFindNodes())
            {
                GD.Print("节点未找到，延迟0.5秒后重试...");
                GetTree().CreateTimer(0.5).Timeout += () => {
                    if (!TryFindNodes())
                    {
                        GD.PrintErr("延迟重试后仍无法找到节点，初始化失败");
                        return;
                    }
                    CompleteInitialization();
                };
                return;
            }

            CompleteInitialization();
        }

        /// <summary>
        /// 尝试查找所需的节点
        /// </summary>
        /// <returns>是否成功找到所有节点</returns>
        private bool TryFindNodes()
        {
            // 查找游戏世界根节点
            _gameWorldRoot = GetNodeOrNull<Node2D>("/root/MainWorld/VisualSorting/GameWorld");
            if (_gameWorldRoot == null)
            {
                if (EnableDebugOutput)
                {
                    GD.Print("GameWorld节点未找到，路径: /root/MainWorld/VisualSorting/GameWorld");
                }
                return false;
            }

            // 查找玩家节点
            _player = GetNodeOrNull<CharacterBody2D>("/root/MainWorld/VisualSorting/player/Player");
            if (_player == null)
            {
                if (EnableDebugOutput)
                {
                    GD.Print("Player节点未找到，路径: /root/MainWorld/VisualSorting/player/Player");
                }
                return false;
            }

            return true;
        }

        /// <summary>
        /// 完成初始化配置
        /// </summary>
        private void CompleteInitialization()
        {
            GD.Print("节点查找成功，开始配置深度排序...");

            // 配置深度排序
            ConfigureYSortForGameWorld();
            ConfigurePlayerDepthSorting();

            GD.Print("深度排序系统初始化完成");
            GD.Print("=== 深度排序管理器初始化结束 ===");
        }

        /// <summary>
        /// 为游戏世界配置Y-Sort
        /// </summary>
        public void ConfigureYSortForGameWorld()
        {
            if (_gameWorldRoot == null) return;

            // 启用GameWorld的Y-Sort
            _gameWorldRoot.YSortEnabled = true;
            
            if (EnableDebugOutput)
            {
                GD.Print($"已为GameWorld启用Y-Sort: {_gameWorldRoot.YSortEnabled}");
            }

            // 配置各个子层的Y-Sort设置
            ConfigureTileMapLayers();
        }

        /// <summary>
        /// 配置TileMapLayer的深度排序
        /// </summary>
        private void ConfigureTileMapLayers()
        {
            // 配置Building层 - 这是关键的建筑物层
            var buildingLayer = _gameWorldRoot.GetNode<TileMapLayer>("Building");
            if (buildingLayer != null)
            {
                // 移除固定的z_index，让Y-Sort接管
                buildingLayer.ZIndex = 0;
                buildingLayer.YSortEnabled = true;
                
                if (EnableDebugOutput)
                {
                    GD.Print($"Building层配置: z_index={buildingLayer.ZIndex}, y_sort={buildingLayer.YSortEnabled}");
                }
            }

            // 配置Plant层
            var plantLayer = _gameWorldRoot.GetNode<TileMapLayer>("Plant");
            if (plantLayer != null)
            {
                plantLayer.ZIndex = 0;
                plantLayer.YSortEnabled = true;
                
                if (EnableDebugOutput)
                {
                    GD.Print($"Plant层配置: z_index={plantLayer.ZIndex}, y_sort={plantLayer.YSortEnabled}");
                }
            }

            // GroundLayer保持在底层
            var groundLayer = _gameWorldRoot.GetNode<TileMapLayer>("GroundLayer");
            if (groundLayer != null)
            {
                groundLayer.ZIndex = -1;
                groundLayer.YSortEnabled = true;
                
                if (EnableDebugOutput)
                {
                    GD.Print($"Ground层配置: z_index={groundLayer.ZIndex}, y_sort={groundLayer.YSortEnabled}");
                }
            }
        }

        /// <summary>
        /// 配置玩家的深度排序
        /// </summary>
        public void ConfigurePlayerDepthSorting()
        {
            if (_player == null) return;

            // 玩家已经在VisualSorting下，无需移动
            // 配置玩家的Y-Sort设置
            _player.YSortEnabled = true;

            // 移除PlayerSprite的固定z_index
            var playerSprite = _player.GetNode<Sprite2D>("PlayerSprite");
            if (playerSprite != null)
            {
                playerSprite.ZIndex = 0; // 让Y-Sort接管

                if (EnableDebugOutput)
                {
                    GD.Print($"PlayerSprite配置: z_index={playerSprite.ZIndex}");
                }
            }

            if (EnableDebugOutput)
            {
                GD.Print($"Player配置: y_sort={_player.YSortEnabled}, position={_player.GlobalPosition}");
            }
        }



        /// <summary>
        /// 获取节点的渲染深度（用于调试）
        /// </summary>
        /// <param name="node">要检查的节点</param>
        /// <returns>渲染深度信息</returns>
        public static string GetNodeDepthInfo(Node2D node)
        {
            if (node == null) return "节点为空";

            return $"节点: {node.Name}, 位置: {node.GlobalPosition}, Z-Index: {node.ZIndex}, Y-Sort: {node.YSortEnabled}";
        }

        /// <summary>
        /// 打印所有相关节点的深度信息（调试用）
        /// </summary>
        public void PrintDepthDebugInfo()
        {
            if (!EnableDebugOutput) return;

            GD.Print("=== 深度排序调试信息 ===");
            
            if (_gameWorldRoot != null)
            {
                GD.Print(GetNodeDepthInfo(_gameWorldRoot));
            }

            if (_player != null)
            {
                GD.Print(GetNodeDepthInfo(_player));
                
                var playerSprite = _player.GetNode<Sprite2D>("PlayerSprite");
                if (playerSprite != null)
                {
                    GD.Print($"  PlayerSprite: Z-Index={playerSprite.ZIndex}");
                }
            }

            // 打印各个层的信息
            var layers = new string[] { "GroundLayer", "Plant", "Building" };
            foreach (var layerName in layers)
            {
                var layer = _gameWorldRoot?.GetNode<TileMapLayer>(layerName);
                if (layer != null)
                {
                    GD.Print($"  {layerName}: Z-Index={layer.ZIndex}, Y-Sort={layer.YSortEnabled}");
                }
            }

            GD.Print("=== 调试信息结束 ===");
        }

        #endregion

        #region 清理

        /// <summary>
        /// 节点退出场景树时清理
        /// </summary>
        public override void _ExitTree()
        {
            if (Instance == this)
            {
                Instance = null;
            }
        }

        #endregion
    }
}
