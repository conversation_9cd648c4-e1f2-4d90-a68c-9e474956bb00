/*
 * 深度排序管理器 - 2D等距视角深度渲染解决方案
 * 负责管理基于Y坐标的深度排序，确保角色和建筑物正确的前后关系
 */

using Godot;

namespace ArchipelagoGame.Manager
{
    /// <summary>
    /// 深度排序管理器 - 处理2D等距视角的深度渲染
    /// 使用Godot 4.x的Y-Sort功能实现基于Y坐标的自动深度排序
    /// </summary>
    public partial class DepthSortManager : Node
    {
        #region 单例模式

        /// <summary>单例实例</summary>
        public static DepthSortManager Instance { get; private set; }

        #endregion

        #region 导出属性

        /// <summary>是否启用调试信息输出</summary>
        [Export]
        public bool EnableDebugOutput { get; set; } = true;

        /// <summary>深度排序的基础偏移值</summary>
        [Export]
        public int BaseDepthOffset { get; set; } = 1000;

        #endregion

        #region 私有字段

        /// <summary>游戏世界根节点引用</summary>
        private Node2D _gameWorldRoot;

        /// <summary>玩家节点引用</summary>
        private CharacterBody2D _player;

        /// <summary>是否已经初始化</summary>
        private bool _isInitialized = false;

        /// <summary>MainWorld场景路径</summary>
        private const string MainWorldScenePath = "res://src/Scenes/GameViwes/MainWorld.tscn";

        #endregion

        #region Godot生命周期

        /// <summary>
        /// 节点准备就绪时初始化
        /// </summary>
        public override void _Ready()
        {
            // 设置单例
            if (Instance == null)
            {
                Instance = this;
            }
            else
            {
                QueueFree();
                return;
            }

            GD.Print("DepthSortManager已加载，等待MainWorld场景");

            // 监听场景切换事件
            GetTree().NodeAdded += OnNodeAdded;
            GetTree().NodeRemoved += OnNodeRemoved;

            // 检查当前场景是否为MainWorld
            CallDeferred(MethodName.CheckCurrentScene);
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 检查当前场景是否为MainWorld
        /// </summary>
        private void CheckCurrentScene()
        {
            var currentScene = GetTree().CurrentScene;
            var scenePath = currentScene?.SceneFilePath ?? "";

            GD.Print($"检查当前场景: {currentScene?.Name ?? "null"}, 路径: {scenePath}");

            if (IsMainWorldScene(scenePath))
            {
                GD.Print("检测到MainWorld场景，开始初始化深度排序");
                InitializeDepthSorting();
            }
            else
            {
                GD.Print("当前不是MainWorld场景，跳过深度排序初始化");
            }
        }

        /// <summary>
        /// 检查是否为MainWorld场景
        /// </summary>
        /// <param name="scenePath">场景路径</param>
        /// <returns>是否为MainWorld场景</returns>
        private bool IsMainWorldScene(string scenePath)
        {
            return scenePath == MainWorldScenePath;
        }

        /// <summary>
        /// 节点添加事件处理
        /// </summary>
        /// <param name="node">添加的节点</param>
        private void OnNodeAdded(Node node)
        {
            // 检查是否是MainWorld场景的根节点
            if (node.Name == "MainWorld" && !_isInitialized)
            {
                GD.Print("检测到MainWorld节点添加，延迟初始化深度排序");
                CallDeferred(MethodName.InitializeDepthSorting);
            }
        }

        /// <summary>
        /// 节点移除事件处理
        /// </summary>
        /// <param name="node">移除的节点</param>
        private void OnNodeRemoved(Node node)
        {
            // 如果MainWorld节点被移除，重置初始化状态
            if (node.Name == "MainWorld" && _isInitialized)
            {
                GD.Print("MainWorld节点被移除，重置深度排序状态");
                ResetDepthSorting();
            }
        }

        /// <summary>
        /// 初始化深度排序系统
        /// </summary>
        public void InitializeDepthSorting()
        {
            // 如果已经初始化，跳过
            if (_isInitialized)
            {
                GD.Print("深度排序已经初始化，跳过");
                return;
            }

            GD.Print("=== 深度排序管理器初始化开始 ===");

            // 打印当前场景信息用于调试
            var currentScene = GetTree().CurrentScene;
            GD.Print($"当前场景: {currentScene?.Name ?? "null"}, 路径: {currentScene?.SceneFilePath ?? "null"}");

            // 检查是否为MainWorld场景
            if (!IsMainWorldScene(currentScene?.SceneFilePath ?? ""))
            {
                GD.Print("当前场景不是MainWorld，跳过深度排序初始化");
                return;
            }

            // 尝试查找节点，如果失败则延迟重试
            if (!TryFindNodes())
            {
                GD.Print("节点未找到，延迟0.5秒后重试...");
                GetTree().CreateTimer(0.5).Timeout += () => {
                    GD.Print("=== 第一次重试开始 ===");
                    if (!TryFindNodes())
                    {
                        GD.Print("第一次重试失败，延迟1.0秒后再次重试...");
                        GetTree().CreateTimer(1.0).Timeout += () => {
                            GD.Print("=== 第二次重试开始 ===");
                            if (!TryFindNodes())
                            {
                                GD.PrintErr("延迟重试后仍无法找到节点，初始化失败");
                                PrintSceneTreeDebugInfo();
                                return;
                            }
                            CompleteInitialization();
                        };
                        return;
                    }
                    CompleteInitialization();
                };
                return;
            }

            CompleteInitialization();
        }

        /// <summary>
        /// 尝试查找所需的节点
        /// </summary>
        /// <returns>是否成功找到所有节点</returns>
        private bool TryFindNodes()
        {
            GD.Print("开始查找节点...");

            // 首先检查MainWorld场景是否存在
            var mainWorld = GetNodeOrNull<Node2D>("/root/MainWorld");
            if (mainWorld == null)
            {
                GD.Print("MainWorld场景节点未找到，路径: /root/MainWorld");
                return false;
            }
            GD.Print("MainWorld场景节点找到");

            // 查找VisualSorting节点
            var visualSorting = mainWorld.GetNodeOrNull<Node2D>("VisualSorting");
            if (visualSorting == null)
            {
                GD.Print("VisualSorting节点未找到，路径: MainWorld/VisualSorting");
                return false;
            }
            GD.Print("VisualSorting节点找到");

            // 查找游戏世界根节点
            _gameWorldRoot = visualSorting.GetNodeOrNull<Node2D>("GameWorld");
            if (_gameWorldRoot == null)
            {
                GD.Print("GameWorld节点未找到，路径: VisualSorting/GameWorld");
                return false;
            }
            GD.Print("GameWorld节点找到");

            // 查找玩家实例节点
            var playerInstance = visualSorting.GetNodeOrNull<Node>("player");
            if (playerInstance == null)
            {
                GD.Print("player实例节点未找到，路径: VisualSorting/player");
                return false;
            }
            GD.Print("player实例节点找到");

            // 查找玩家CharacterBody2D节点
            _player = playerInstance.GetNodeOrNull<CharacterBody2D>("Player");
            if (_player == null)
            {
                GD.Print("Player CharacterBody2D节点未找到，路径: player/Player");
                return false;
            }
            GD.Print("Player CharacterBody2D节点找到");

            GD.Print("所有节点查找成功！");
            return true;
        }

        /// <summary>
        /// 完成初始化配置
        /// </summary>
        private void CompleteInitialization()
        {
            GD.Print("节点查找成功，开始配置深度排序...");

            // 配置深度排序
            ConfigureYSortForGameWorld();
            ConfigurePlayerDepthSorting();

            // 标记为已初始化
            _isInitialized = true;

            GD.Print("深度排序系统初始化完成");
            GD.Print("=== 深度排序管理器初始化结束 ===");
        }

        /// <summary>
        /// 重置深度排序状态
        /// </summary>
        private void ResetDepthSorting()
        {
            _isInitialized = false;
            _gameWorldRoot = null;
            _player = null;
            GD.Print("深度排序状态已重置");
        }

        /// <summary>
        /// 为游戏世界配置Y-Sort
        /// </summary>
        public void ConfigureYSortForGameWorld()
        {
            if (_gameWorldRoot == null) return;

            // 启用GameWorld的Y-Sort
            _gameWorldRoot.YSortEnabled = true;
            
            if (EnableDebugOutput)
            {
                GD.Print($"已为GameWorld启用Y-Sort: {_gameWorldRoot.YSortEnabled}");
            }

            // 配置各个子层的Y-Sort设置
            ConfigureTileMapLayers();
        }

        /// <summary>
        /// 配置TileMapLayer的深度排序
        /// </summary>
        private void ConfigureTileMapLayers()
        {
            // 配置Building层 - 这是关键的建筑物层
            var buildingLayer = _gameWorldRoot.GetNode<TileMapLayer>("Building");
            if (buildingLayer != null)
            {
                // 移除固定的z_index，让Y-Sort接管
                buildingLayer.ZIndex = 0;
                buildingLayer.YSortEnabled = true;
                
                if (EnableDebugOutput)
                {
                    GD.Print($"Building层配置: z_index={buildingLayer.ZIndex}, y_sort={buildingLayer.YSortEnabled}");
                }
            }

            // 配置Plant层
            var plantLayer = _gameWorldRoot.GetNode<TileMapLayer>("Plant");
            if (plantLayer != null)
            {
                plantLayer.ZIndex = 0;
                plantLayer.YSortEnabled = true;
                
                if (EnableDebugOutput)
                {
                    GD.Print($"Plant层配置: z_index={plantLayer.ZIndex}, y_sort={plantLayer.YSortEnabled}");
                }
            }

            // GroundLayer保持在底层
            var groundLayer = _gameWorldRoot.GetNode<TileMapLayer>("GroundLayer");
            if (groundLayer != null)
            {
                groundLayer.ZIndex = -1;
                groundLayer.YSortEnabled = true;
                
                if (EnableDebugOutput)
                {
                    GD.Print($"Ground层配置: z_index={groundLayer.ZIndex}, y_sort={groundLayer.YSortEnabled}");
                }
            }
        }

        /// <summary>
        /// 配置玩家的深度排序
        /// </summary>
        public void ConfigurePlayerDepthSorting()
        {
            if (_player == null) return;

            // 玩家已经在VisualSorting下，无需移动
            // 配置玩家的Y-Sort设置
            _player.YSortEnabled = true;

            // 移除PlayerSprite的固定z_index
            var playerSprite = _player.GetNode<Sprite2D>("PlayerSprite");
            if (playerSprite != null)
            {
                playerSprite.ZIndex = 0; // 让Y-Sort接管

                if (EnableDebugOutput)
                {
                    GD.Print($"PlayerSprite配置: z_index={playerSprite.ZIndex}");
                }
            }

            if (EnableDebugOutput)
            {
                GD.Print($"Player配置: y_sort={_player.YSortEnabled}, position={_player.GlobalPosition}");
            }
        }



        /// <summary>
        /// 获取节点的渲染深度（用于调试）
        /// </summary>
        /// <param name="node">要检查的节点</param>
        /// <returns>渲染深度信息</returns>
        public static string GetNodeDepthInfo(Node2D node)
        {
            if (node == null) return "节点为空";

            return $"节点: {node.Name}, 位置: {node.GlobalPosition}, Z-Index: {node.ZIndex}, Y-Sort: {node.YSortEnabled}";
        }

        /// <summary>
        /// 打印所有相关节点的深度信息（调试用）
        /// </summary>
        public void PrintDepthDebugInfo()
        {
            if (!EnableDebugOutput) return;

            GD.Print("=== 深度排序调试信息 ===");

            if (_gameWorldRoot != null)
            {
                GD.Print(GetNodeDepthInfo(_gameWorldRoot));
            }

            if (_player != null)
            {
                GD.Print(GetNodeDepthInfo(_player));

                var playerSprite = _player.GetNode<Sprite2D>("PlayerSprite");
                if (playerSprite != null)
                {
                    GD.Print($"  PlayerSprite: Z-Index={playerSprite.ZIndex}");
                }
            }

            // 打印各个层的信息
            var layers = new string[] { "GroundLayer", "Plant", "Building" };
            foreach (var layerName in layers)
            {
                var layer = _gameWorldRoot?.GetNode<TileMapLayer>(layerName);
                if (layer != null)
                {
                    GD.Print($"  {layerName}: Z-Index={layer.ZIndex}, Y-Sort={layer.YSortEnabled}");
                }
            }

            GD.Print("=== 调试信息结束 ===");
        }

        /// <summary>
        /// 打印场景树调试信息
        /// </summary>
        private void PrintSceneTreeDebugInfo()
        {
            GD.Print("=== 场景树调试信息 ===");

            var currentScene = GetTree().CurrentScene;
            GD.Print($"当前场景: {currentScene?.Name ?? "null"}");
            GD.Print($"场景路径: {currentScene?.SceneFilePath ?? "null"}");

            // 检查root节点下的直接子节点
            var root = GetTree().Root;
            GD.Print("Root节点的子节点:");
            foreach (Node child in root.GetChildren())
            {
                GD.Print($"  - {child.Name} ({child.GetType().Name})");
            }

            // 如果MainWorld存在，打印其子节点
            var mainWorld = GetNodeOrNull<Node>("/root/MainWorld");
            if (mainWorld != null)
            {
                GD.Print("MainWorld的子节点:");
                foreach (Node child in mainWorld.GetChildren())
                {
                    GD.Print($"  - {child.Name} ({child.GetType().Name})");

                    // 如果是VisualSorting，打印其子节点
                    if (child.Name == "VisualSorting")
                    {
                        GD.Print("    VisualSorting的子节点:");
                        foreach (Node grandChild in child.GetChildren())
                        {
                            GD.Print($"      - {grandChild.Name} ({grandChild.GetType().Name})");
                        }
                    }
                }
            }
            else
            {
                GD.Print("MainWorld节点不存在");
            }

            GD.Print("=== 场景树调试信息结束 ===");
        }

        /// <summary>
        /// 手动重新初始化深度排序系统
        /// 用于场景切换后重新初始化
        /// </summary>
        public void ReinitializeDepthSorting()
        {
            GD.Print("手动重新初始化深度排序系统");
            ResetDepthSorting();
            InitializeDepthSorting();
        }

        /// <summary>
        /// 强制初始化深度排序系统（忽略场景检查）
        /// 用于特殊情况下的强制初始化
        /// </summary>
        public void ForceInitializeDepthSorting()
        {
            GD.Print("强制初始化深度排序系统");
            ResetDepthSorting();

            // 直接尝试查找节点，不检查场景类型
            if (!TryFindNodes())
            {
                GD.PrintErr("强制初始化失败：无法找到节点");
                PrintSceneTreeDebugInfo();
                return;
            }

            CompleteInitialization();
        }

        #endregion

        #region 清理

        /// <summary>
        /// 节点退出场景树时清理
        /// </summary>
        public override void _ExitTree()
        {
            // 清理事件监听
            if (GetTree() != null)
            {
                GetTree().NodeAdded -= OnNodeAdded;
                GetTree().NodeRemoved -= OnNodeRemoved;
            }

            if (Instance == this)
            {
                Instance = null;
            }
        }

        #endregion
    }
}
