/*
 * MainWorld场景控制器
 * 负责确保MainWorld场景正确初始化，包括深度排序系统
 */

using Godot;
using ArchipelagoGame.Manager;

namespace ArchipelagoGame.Scenes
{
    /// <summary>
    /// MainWorld场景控制器
    /// 确保场景完全加载后初始化相关系统
    /// </summary>
    public partial class MainWorldController : Node2D
    {
        #region Godot生命周期

        /// <summary>
        /// 场景准备就绪时调用
        /// </summary>
        public override void _Ready()
        {
            GD.Print("MainWorld场景控制器初始化");
            
            // 延迟初始化，确保所有子节点都已加载
            CallDeferred(MethodName.InitializeScene);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化场景
        /// </summary>
        private void InitializeScene()
        {
            GD.Print("MainWorld场景开始初始化");
            
            // 确保DepthSortManager重新初始化
            if (DepthSortManager.Instance != null)
            {
                GD.Print("重新初始化DepthSortManager");
                DepthSortManager.Instance.ReinitializeDepthSorting();
            }
            else
            {
                GD.PrintErr("DepthSortManager实例不存在");
            }
            
            GD.Print("MainWorld场景初始化完成");
        }

        #endregion
    }
}
